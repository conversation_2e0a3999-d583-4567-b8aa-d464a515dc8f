<?php
// +----------------------------------------------------------------------
// | Longbing [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright Chengdu longbing Technology Co., Ltd.
// +----------------------------------------------------------------------
// | Website http://longbing.org/
// +----------------------------------------------------------------------
// | Sales manager: +86-13558882532 / +86-13330887474
// | Technical support: +86-15680635005
// | After-sale service: +86-17361005938
// +----------------------------------------------------------------------


declare(strict_types=1);


namespace app\adapay\info;

use longbingcore\permissions\PermissionAbstract;

/**
 * 商城模块功能权限
 * Class PermissionAppstore
 */
class PermissionAdapay extends PermissionAbstract {

    const tabbarKey = null;
    //后台管理菜单对应key[必填] , 当前模块文件夹名称
    const adminMenuKey = 'adapay';
    public $saasKey ;
    const apiPaths = [];


    public function __construct(int $uniacid,$infoConfigOptions=[],$saasKey='ADAPAY')
    {
        $this->saasKey  = longbing_get_auth_prefix($saasKey) ;
        parent::__construct($uniacid, self::tabbarKey, self::adminMenuKey, $this->saasKey, self::apiPaths , $infoConfigOptions);
    }


    /**
     * 返回saas端授权结果
     * @return bool
     */
    public function sAuth(): bool
    {

        return  $this->sassValue == 1 ? true : false;
    }

    /**
     * 返回p端授权结果
     * @return bool
     */
    public function pAuth(): bool
    {

        if (!$this->sAuth()) {

            return  false;
        };

        return true;

    }

    /**
     * 返回c端授权结果
     *
     * @param int $user_id
     * @return bool
     * <AUTHOR>
     * @DataTime: 2019/12/9 17:13
     */
    public function cAuth(int $user_id): bool
    {

        return true;

    }



    /**
     * 添加商品数量
     *
     * <AUTHOR>
     * @DataTime: 2019/12/19 19:02
     */
    public function getCityNumber(){

        return $this->getAuthVaule(  longbing_get_auth_prefix('CITY') , 1);

    }



    /**
     * 添加商品数量
     *
     * <AUTHOR>
     * @DataTime: 2019/12/19 19:02
     */
    public function getAuthPhone(){

        return $this->getAuthVaule(  longbing_get_auth_prefix('PHONE') , 0,1);

    }


    /**
     * 添加商品数量
     *
     * <AUTHOR>
     * @DataTime: 2019/12/19 19:02
     */
    public function getAuthPhoneAuth(){

        return $this->getAuthVaule(  longbing_get_auth_prefix('PHONE_AUTH') , 0,1);

    }

}