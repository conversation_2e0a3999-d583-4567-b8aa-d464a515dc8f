<?php
namespace app\balancediscount\model;

use app\BaseModel;
use think\facade\Db;

class Card extends BaseModel
{
    //定义表名
    protected $name = 'massage_balance_discount_card_list';


    public function getPriceAttr($value,$data){

        if(isset($value)){

            return round($value,2);
        }
    }



    public function getDiscountAttr($value,$data){

        if(isset($value)){

            return round($value,1);
        }
    }


    /**
     * <AUTHOR>
     * @DataTime: 2020-09-29 11:04
     * @功能说明:添加
     */
    public function dataAdd($data){

        $res = $this->insert($data);

        return $res;

    }



    /**
     * <AUTHOR>
     * @DataTime: 2020-09-29 11:05
     * @功能说明:编辑
     */
    public function dataUpdate($dis,$data){

        $res = $this->where($dis)->update($data);

        return $res;

    }


    /**
     * <AUTHOR>
     * @DataTime: 2020-09-29 11:06
     * @功能说明:列表
     */
    public function dataList($dis,$page){

        $data = $this->where($dis)->order('top desc,id desc')->paginate($page)->toArray();

        return $data;

    }


    /**
     * <AUTHOR>
     * @DataTime: 2020-09-29 11:43
     * @功能说明:
     */
    public function dataInfo($dis){

        $data = $this->where($dis)->find();

        return !empty($data)?$data->toArray():[];

    }












}