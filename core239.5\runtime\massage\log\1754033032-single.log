[2025-08-01T15:23:50+08:00][sql] CONNECT:[ UseTime:0.001169s ] mysql:host=127.0.0.1;port=3306;dbname=peiwan2;charset=utf8mb4
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_shequshop_school_admin` [ RunTime:0.004465s ]
[2025-08-01T15:23:50+08:00][sql] SELECT * FROM `ims_shequshop_school_admin` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000764s ]
[2025-08-01T15:23:50+08:00][sql] SELECT * FROM `ims_shequshop_school_admin` WHERE  `agent_name` = '' [ RunTime:0.000830s ]
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_massage_config_setting` [ RunTime:0.003043s ]
[2025-08-01T15:23:50+08:00][sql] SELECT * FROM `ims_massage_config_setting` WHERE  `uniacid` = 666  AND `key` IN ('have_order_notice','order_tmpl_notice','nopay_notice') [ RunTime:0.000904s ]
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_massage_service_notice_list` [ RunTime:0.002153s ]
[2025-08-01T15:23:50+08:00][sql] SELECT COUNT(*) AS think_count FROM ( SELECT `a`.*,`b`.`is_add`,c.is_add as refund_add,count(*) AS think_count FROM `ims_massage_service_notice_list` `a` LEFT JOIN `ims_massage_service_order_list` `b` ON `a`.`order_id`=`b`.`id` LEFT JOIN `ims_massage_service_refund_order` `c` ON `a`.`order_id`=`c`.`id` WHERE  `a`.`uniacid` = '666'  AND `a`.`have_look` = '0' GROUP BY `a`.`id` ) `_group_count_` LIMIT 1 [ RunTime:0.000724s ]
[2025-08-01T15:23:50+08:00][sql] SELECT `a`.*,`b`.`is_add`,c.is_add as refund_add FROM `ims_massage_service_notice_list` `a` LEFT JOIN `ims_massage_service_order_list` `b` ON `a`.`order_id`=`b`.`id` LEFT JOIN `ims_massage_service_refund_order` `c` ON `a`.`order_id`=`c`.`id` WHERE  `a`.`uniacid` = '666'  AND `a`.`have_look` = '0'  AND `a`.`is_pop` = '0' GROUP BY `a`.`id` ORDER BY `a`.`id` DESC LIMIT 1 [ RunTime:0.000612s ]
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_massage_service_coach_police` [ RunTime:0.002667s ]
[2025-08-01T15:23:50+08:00][sql] SELECT COUNT(*) AS think_count FROM ( SELECT count(*) AS think_count FROM `ims_massage_service_coach_police` `a` INNER JOIN `ims_massage_service_coach_list` `b` ON `a`.`coach_id`=`b`.`id` WHERE  `a`.`uniacid` = '666'  AND `a`.`have_look` = '0'  AND `a`.`status` > '-1' GROUP BY `a`.`id` ) `_group_count_` LIMIT 1 [ RunTime:0.000678s ]
[2025-08-01T15:23:50+08:00][sql] SELECT * FROM `ims_shequshop_school_admin` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000868s ]
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_massage_service_abnormal_order_list` [ RunTime:0.002079s ]
[2025-08-01T15:23:50+08:00][sql] SELECT COUNT(*) AS think_count FROM ( SELECT count(*) AS think_count FROM `ims_massage_service_abnormal_order_list` `a` INNER JOIN `ims_massage_service_order_list` `c` ON `a`.`order_id`=`c`.`id` LEFT JOIN `ims_massage_service_abnormal_order_role` `d` ON `d`.`process_id`=a.process_id AND a.pass_type = d.pass_type LEFT JOIN `ims_massage_service_abnormal_order_info_handle` `e` ON `a`.`info_id`=e.order_info_id AND e.user_id in (1) WHERE  `a`.`uniacid` = '666'  AND `a`.`is_handle` = '0'  AND `a`.`first_cancel` = '0'  AND `e`.`id` IS NULL  AND (  `a`.`uniacid` = '666'  OR `a`.`uniacid` = '666' ) GROUP BY `a`.`id` ) `_group_count_` LIMIT 1 [ RunTime:0.000963s ]
[2025-08-01T15:23:50+08:00][sql] SHOW FULL COLUMNS FROM `ims_massage_service_order_list` [ RunTime:0.007145s ]
[2025-08-01T15:23:50+08:00][sql] SELECT *,round(init_service_price+if(init_material_price>0,init_material_price,material_price)-service_price-if(start_material_price>0,start_material_price,0),2) as discount_price FROM `ims_massage_service_order_list` WHERE  `have_sql` = 1  AND `pay_time` > 0  AND `uniacid` = 666  AND `pay_type` IN (-1,7) LIMIT 200 [ RunTime:0.001781s ]
