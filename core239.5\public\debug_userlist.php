<?php
// 调试userList接口的测试文件
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('memory_limit', '512M');

echo "<h1>UserList Debug Test</h1>";

try {
    // 1. 测试基本PHP环境
    echo "<h2>1. PHP Environment Test</h2>";
    echo "PHP Version: " . phpversion() . "<br>";
    echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
    echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
    
    // 2. 测试数据库连接
    echo "<h2>2. Database Connection Test</h2>";
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=peiwan2;charset=utf8mb4', 'root', '123456789');
    echo "Database connection: OK<br>";
    
    // 3. 测试Redis连接
    echo "<h2>3. Redis Connection Test</h2>";
    if (extension_loaded('redis')) {
        $redis = new Redis();
        $result = $redis->connect('127.0.0.1', 6379);
        if ($result) {
            echo "Redis connection: OK<br>";
            $redis->close();
        } else {
            echo "Redis connection: FAILED<br>";
        }
    } else {
        echo "Redis extension: NOT LOADED<br>";
    }
    
    // 4. 测试ThinkPHP框架加载
    echo "<h2>4. ThinkPHP Framework Test</h2>";
    require __DIR__ . '/../vendor/autoload.php';
    echo "Autoload: OK<br>";

    // 初始化ThinkPHP应用
    $app = new \think\App();
    $app->initialize();
    echo "ThinkPHP App initialized: OK<br>";

    // 加载common.php文件
    require __DIR__ . '/../app/common.php';
    echo "Common.php loaded: OK<br>";

    // 测试longbing_get_auth_prefix函数
    if (function_exists('longbing_get_auth_prefix')) {
        echo "longbing_get_auth_prefix function: EXISTS<br>";
        try {
            $test_prefix = longbing_get_auth_prefix('TEST');
            echo "Test prefix result: " . $test_prefix . "<br>";
        } catch (Exception $e) {
            echo "Test prefix error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "longbing_get_auth_prefix function: NOT EXISTS<br>";
    }
    
    // 5. 测试用户表查询
    echo "<h2>5. User Table Query Test</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ims_massage_service_user_list WHERE uniacid = 666");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "User count: " . $result['count'] . "<br>";
    
    // 6. 测试用户数据查询
    echo "<h2>6. User Data Query Test</h2>";
    $stmt = $pdo->prepare("SELECT * FROM ims_massage_service_user_list WHERE uniacid = 666 AND user_status = 1 ORDER BY id DESC LIMIT 5");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Sample users count: " . count($users) . "<br>";
    
    if (!empty($users)) {
        echo "First user ID: " . $users[0]['id'] . "<br>";
        echo "First user nickname: " . ($users[0]['nickName'] ?? 'N/A') . "<br>";
    }
    
    // 7. 测试相关表是否存在
    echo "<h2>7. Related Tables Test</h2>";
    $tables = [
        'ims_massage_balance_discount_user_card',
        'ims_massage_service_user_label_data',
        'ims_member_level',
        'ims_massage_service_channel_scan_qr',
        'ims_memberdiscount_card'
    ];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM $table LIMIT 1");
            $stmt->execute();
            echo "Table $table: EXISTS<br>";
        } catch (Exception $e) {
            echo "Table $table: NOT EXISTS or ERROR - " . $e->getMessage() . "<br>";
        }
    }
    
    // 8. 测试ThinkPHP模型加载
    echo "<h2>8. ThinkPHP Models Test</h2>";
    
    try {
        // 初始化ThinkPHP应用
        $app = new \think\App();
        echo "ThinkPHP App: OK<br>";
        
        // 测试用户模型
        $userModel = new \app\massage\model\User();
        echo "User Model: OK<br>";
        
        // 测试权限模型
        $permissionMember = new \app\member\info\PermissionMember(666);
        echo "Permission Member: OK<br>";
        
        $permissionBalanceDiscount = new \app\balancediscount\info\PermissionBalancediscount(666);
        echo "Permission Balance Discount: OK<br>";
        
    } catch (Exception $e) {
        echo "ThinkPHP Models Error: " . $e->getMessage() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>9. Memory Usage</h2>";
    echo "Current memory usage: " . memory_get_usage(true) / 1024 / 1024 . " MB<br>";
    echo "Peak memory usage: " . memory_get_peak_usage(true) / 1024 / 1024 . " MB<br>";
    
} catch (Exception $e) {
    echo "<h2>ERROR OCCURRED</h2>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test Completed</h2>";
?>
